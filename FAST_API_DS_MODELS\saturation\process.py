import pandas as pd
import numpy as np
import warnings
import os
import sys
from pathlib import Path
from sklearn.preprocessing import QuantileTransformer, PolynomialFeatures
from sklearn.linear_model import Ridge, LinearRegression
from sklearn.pipeline import make_pipeline
from sklearn.metrics import r2_score
from scipy.stats import gaussian_kde
from matplotlib.backends.backend_pdf import PdfPages
import matplotlib.pyplot as plt

warnings.filterwarnings("ignore")

# Setup path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from db_writer import DBWriter
from shared_config import get_config
from saturation.utils import (
    poly_func,
    find_input_given_output,
    plot_gaussian_analysis,
    plot_actual_values_with_saturation,
    sanitize_filename,
    plot_combined_subplot,
    save_plot_data_to_excel,
    resolve_template_path,
    convert_arrays_to_lists
)


class SaturationAnalyzer:
    def __init__(self):
        self.cfg = get_config()
        self.input_file = self.cfg["INPUT_FILE"]
        self.saturation_template = self.cfg["SATURATION_FILE"]
        self.plot_data_template = self.cfg["PLOT_DATA_FILE"]
        self.min_data_points = self.cfg.get("MIN_DATA_POINTS", 10)
        self.plots_dir = Path(self.cfg.get("PLOTS_DIR", "data/output/plots"))
        self.perform_cluster_analysis = self.cfg.get("PERFORM_CLUSTER_LEVEL_ANALYSIS", True)

        overall_perf_table = self.cfg["DATABASE_CONFIG"]["RESULT_TABLES"]["MAIN_OUTPUT"]
        # self.df = pd.read_excel(self.input_file)
        writer = DBWriter(self.cfg)
        self.df = writer.read(        
        table_name=overall_perf_table,
        # latest_by="created_at",
        latest_only=True)

        print(f"Reading data from {overall_perf_table}",self.df.shape)
        print("Before dropna, shape:", self.df.shape)
        print("TOTAL_LM null count:", self.df["TOTAL_LM"].isna().sum())
        print("Performance null count:", self.df["Performance"].isna().sum())

        # self.df = self.df.dropna(subset=["TOTAL_LM", "Performance"])
        self.df = self.df.dropna(subset=["TOTAL_LM"])
        self.df["positive_meter"] = self.df["TOTAL_LM"] + 1e-6
        print("Before astype, shape:", self.df.shape)
        print("Null count in CLUSTER_NUM:", self.df["CLUSTER_NUM"].isna().sum())
        print("Unique values in CLUSTER_NUM:", self.df["CLUSTER_NUM"].unique())
        self.df["CLUSTER_NUM"] = self.df["CLUSTER_NUM"].astype(int)
        self.df = self.df.sort_values(by=["SUB_CLSS_NM", "positive_meter"])

    def run(self):
        try:
            print("perform_cluster_analysis =", self.perform_cluster_analysis)
            print('"CLUSTER_NUM" in columns =', "CLUSTER_NUM" in self.df.columns)
            if self.perform_cluster_analysis and "CLUSTER_NUM" in self.df.columns:
                print("Running cluster-wise saturation analysis...")
                print("type of cluster_name",self.df["CLUSTER_NUM"].dtype)
                print("Shape of df:", self.df.shape)
                print("Unique values in CLUSTER_NUM:", self.df["CLUSTER_NUM"].unique())
                print("Null count in CLUSTER_NUM:", self.df["CLUSTER_NUM"].isna().sum())
                for cluster_name, df_cluster in self.df.groupby("CLUSTER_NUM"):
                    print(f"Running saturation analysis for cluster: {cluster_name}")
                    self._run_saturation_analysis(df_cluster, cluster_name)
            else:
                print("Running overall saturation analysis (no cluster split)...")
                self._run_saturation_analysis(self.df, "overall")
        except Exception as e:
            print(f"Error running saturation analysis: {str(e)}")
    def _run_saturation_analysis(self, df_subset, cluster_name="overall"):
        results, plot_data_rows = [], []

        safe_cluster_name = sanitize_filename(str(cluster_name))
        cluster_plot_dir = self.plots_dir / safe_cluster_name
        cluster_plot_dir.mkdir(parents=True, exist_ok=True)
        pdf_path = cluster_plot_dir / "all_subclass_plots.pdf"
        pdf_pages = PdfPages(pdf_path)

        for subclass in df_subset["SUB_CLSS_NM"].unique():
            df_sub = df_subset[df_subset["SUB_CLSS_NM"] == subclass]
            if len(df_sub) < self.min_data_points:
                continue

            grp_nm = df_sub["GRP_NM"].iloc[0]
            dpt_nm = df_sub["DPT_NM"].iloc[0]
            clss_nm = df_sub["CLSS_NM"].iloc[0]

            qt = QuantileTransformer(output_distribution='normal', random_state=42,
                                     n_quantiles=min(100, len(df_sub)))
            X_raw = df_sub["positive_meter"].values.reshape(-1, 1)
            X_gauss = qt.fit_transform(X_raw)
            y = df_sub["Performance"].values

            best_r2, best_coeffs = -np.inf, None
            for degree in range(2, min(5, max(2, len(y) // 2)) + 1):
                ridge = make_pipeline(PolynomialFeatures(degree), Ridge(alpha=0.1))
                ridge.fit(X_gauss, y)
                pred_r2 = r2_score(y, ridge.predict(X_gauss))

                lin = make_pipeline(PolynomialFeatures(degree), LinearRegression())
                lin.fit(X_gauss, y)
                lin_r2 = r2_score(y, lin.predict(X_gauss))

                if abs(pred_r2 - lin_r2) <= 1 and pred_r2 > best_r2:
                    best_r2 = pred_r2
                    best_coeffs = np.linalg.lstsq(
                        PolynomialFeatures(degree).fit_transform(X_gauss), y, rcond=None
                    )[0]
                    best_normal_r2, best_ridge_r2 = lin_r2, pred_r2

            if best_coeffs is None:
                continue

            gauss_range = np.linspace(X_gauss.min(), X_gauss.max(), 200)
            y_pred = poly_func(gauss_range, *best_coeffs)
            density = gaussian_kde(X_gauss.flatten())(gauss_range)

            init_sat_gauss = gauss_range[np.argmax(density)]
            perf_at_sat = poly_func(init_sat_gauss, *best_coeffs)
            refined_sat = find_input_given_output(
                lambda x: poly_func(x, *best_coeffs),
                perf_at_sat, [X_gauss.min(), X_gauss.max()]
            ) or init_sat_gauss
            sat_lm = qt.inverse_transform([[refined_sat]])[0][0]

            plot_data_rows.append({
                'Cluster': cluster_name,
                'GRP_NM': grp_nm,
                'DPT_NM': dpt_nm,
                'CLSS_NM': clss_nm,
                'SUB_CLSS_NM': subclass,
                'x_raw': X_raw.flatten().tolist(),
                'x_gauss': X_gauss.flatten().tolist(),
                'y': y.tolist(),
                'y_pred': y_pred.tolist(),
                'density': density.tolist(),
                'refined_sat_gauss': float(refined_sat),
                'sat_lm': round(float(sat_lm), 3),
                'perf_at_sat': round(float(perf_at_sat), 3)
            })

            results.append({
                'Cluster': cluster_name,
                'GRP_NM': grp_nm,
                'DPT_NM': dpt_nm,
                'CLSS_NM': clss_nm,
                'SUB_CLSS_NM': subclass,
                'max_saturation_point': round(sat_lm, 3),
                'PerformanceAchieved': round(perf_at_sat, 3),
                'Normal_R2': best_normal_r2,
                'Ridge_R2': best_ridge_r2
            })

        # Save Excel
        saturation_path = resolve_template_path(self.saturation_template, cluster_name)
        # pd.DataFrame(results).to_excel(saturation_path, index=False)
        saturation_data_df = pd.DataFrame(results)
        # Apply this function row-wise
        # saturation_data_df = plot_data_rows.apply(convert_arrays_to_lists, axis=1)
        
        writer = DBWriter(self.cfg)
        writer.write(saturation_data_df, "SATURATION_DATA")

        # Save Plot Data
        plot_data_path = resolve_template_path(self.plot_data_template, cluster_name)
        save_plot_data_to_excel(plot_data_rows, cluster_name, plot_data_path)

        pdf_pages.close()


if __name__ == "__main__":
    analyzer = SaturationAnalyzer()
    analyzer.run()
